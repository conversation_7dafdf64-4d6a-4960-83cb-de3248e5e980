
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多重确认阶段性止盈止损策略 (单次交易回测模型)
作者: AI Assistant
版本: 3.2 (修正了 testBuy 函数中的 NameError bug)
适用场景: 提供一个价格序列，执行一次完整的、基于K线价格的多头策略回测
"""

import numpy as np
import pandas as pd
from typing import Dict, Optional, List, Tuple
from datetime import datetime
import json
import warnings
warnings.filterwarnings('ignore', category=UserWarning)

# --- 猴子补丁开始 ---
if not hasattr(np, 'NaN'):
    np.NaN = np.nan
# --- 猴子补丁结束 ---

class LiveTradingStrategy:
    """
    实盘交易多重确认阶段性止盈止损策略类 (支持分批止盈)
    
    核心思想：
    - 阶段 0: 风险期，宽松止损等待确认
    - 阶段 1: 确认期(新增)，等待价格持稳后才收紧止损
    - 阶段 2: 安全期，止损移至盈利区
    - 阶段 3: 跟踪期，动态跟踪止损
    - 分批止盈: 在不同盈利水平逐步锁定利润
    """
    
    def __init__(self, 
                 stage1_threshold: float = 0.005,
                 stage2_threshold: float = 0.015,
                 stage3_threshold: float = 0.025,
                 initial_stop_loss_ratio: float = 0.015,
                 stage1_stop_loss_ratio: float = 0.005,
                 stage1_confirmation_ticks: int = 3,
                 stage2_stop_profit_ratio: float = 0.005,
                 trailing_ratio: float = 0.3,
                 min_profit_lock_ratio: float = 0.002,
                 take_profit_levels: Optional[List[Tuple[float, float]]] = None,
                 enable_logging: bool = True,
                 dynamic_stop_adjustment: bool = True,
                 volatility_window: int = 5,
                 max_drawdown_protection: float = 0.15,
                 hard_stop_loss_ratio: float = 0.04):
        # 策略参数
        self.stage1_threshold = stage1_threshold
        self.stage2_threshold = stage2_threshold
        self.stage3_threshold = stage3_threshold
        self.initial_stop_loss_ratio = initial_stop_loss_ratio
        self.stage1_stop_loss_ratio = stage1_stop_loss_ratio
        self.stage1_confirmation_ticks = stage1_confirmation_ticks
        self.stage2_stop_profit_ratio = stage2_stop_profit_ratio
        self.trailing_ratio = trailing_ratio
        self.min_profit_lock_ratio = min_profit_lock_ratio
        self.take_profit_levels = sorted(take_profit_levels, key=lambda x: x[0]) if take_profit_levels else []
        self.enable_logging = enable_logging
        self.dynamic_stop_adjustment = dynamic_stop_adjustment
        self.volatility_window = volatility_window
        self.max_drawdown_protection = max_drawdown_protection
        self.hard_stop_loss_ratio = hard_stop_loss_ratio
        
        # 参数验证
        self._validate_parameters()

        # 交易状态
        self.reset()

    def _validate_parameters(self):
        """验证策略参数的合理性"""
        if self.stage1_threshold >= self.stage2_threshold:
            raise ValueError("stage1_threshold 应该小于 stage2_threshold")
        if self.stage2_threshold >= self.stage3_threshold:
            raise ValueError("stage2_threshold 应该小于 stage3_threshold")
        if self.initial_stop_loss_ratio <= 0 or self.initial_stop_loss_ratio > 0.5:
            raise ValueError("initial_stop_loss_ratio 应该在 (0, 0.5] 范围内")
        if self.trailing_ratio <= 0 or self.trailing_ratio > 1:
            raise ValueError("trailing_ratio 应该在 (0, 1] 范围内")

        # 验证分批止盈设置
        for i, (profit_level, sell_ratio) in enumerate(self.take_profit_levels):
            if profit_level <= 0:
                raise ValueError(f"take_profit_levels[{i}] 的盈利水平必须大于0")
            if sell_ratio <= 0 or sell_ratio > 1:
                raise ValueError(f"take_profit_levels[{i}] 的卖出比例必须在 (0, 1] 范围内")
    
    def reset(self):
        """重置策略状态"""
        self.position_size = 0.0 
        self.entry_price = 0.0
        self.current_stage = 0
        self.stop_loss = 0.0
        self.high_water_mark = 0.0
        self.entry_time = None
        self.tick_count = 0
        self.stage1_confirmation_counter = 0
        self.take_profit_triggers_hit = [False] * len(self.take_profit_levels)
        self.total_profit_realized = 0.0
        self.position_value_at_entry = 1.0
        self.price_history = []
        self.stop_loss_history = []
        self.stage_history = []
        self.profit_history = []
        self.position_size_history = []
        self.trade_log = []
        self.partial_profit_log = []
        
        if self.enable_logging:
            self._log("策略状态已重置 (多头)")
    
    def _log(self, message: str, level: str = "INFO"):
        """内部日志记录"""
        if self.enable_logging:
            timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
            log_entry = {'timestamp': timestamp, 'tick': self.tick_count, 'level': level, 'message': message}
            self.trade_log.append(log_entry)
    
    def enter_position(self, price: float, signal_info: Optional[Dict] = None) -> Dict:
        """开仓操作 (多头)"""
        if self.position_size > 0:
            self._log("警告: 已有持仓，无法重复开仓", "WARNING")
            return {'success': False, 'reason': 'ALREADY_IN_POSITION', 'message': '已有持仓'}
        
        self.reset() 
        self.position_size = 1.0
        self.entry_price = price
        self.current_stage = 0
        self.stop_loss = price * (1 - self.initial_stop_loss_ratio)
        self.high_water_mark = price
        self.entry_time = datetime.now()
        
        self.price_history.append(price)
        self.stop_loss_history.append(self.stop_loss)
        self.stage_history.append(0)
        self.profit_history.append(0.0)
        self.position_size_history.append(self.position_size)
        
        result = {
            'success': True, 'action': 'ENTER', 'position_type': 'LONG', 
            'price': price, 'stop_loss': self.stop_loss, 'stage': self.current_stage,
            'position_size': self.position_size,
            'message': f"开仓成功: 价格={price:.6f}, 止损={self.stop_loss:.6f}"
        }
        self._log(f"开仓成功 (多头): 价格={price:.6f}, 仓位={self.position_size:.2f}, 止损={self.stop_loss:.6f}")
        return result
    
    def process_tick(self, price: float, timestamp: Optional[datetime] = None) -> Dict:
        """处理单个价格tick"""
        self.tick_count += 1
        
        if self.position_size <= 0:
            return {'success': False, 'action': 'NO_POSITION', 'price': price, 'message': '当前无持仓'}
        
        self.price_history.append(price)
        self.high_water_mark = max(self.high_water_mark, price) 
        current_profit_pct = (price - self.entry_price) / self.entry_price
        
        self._check_partial_take_profit(price, current_profit_pct)
        if self.position_size <= 0:
            return self._exit_position(price, 'ALL_PARTS_TAKEN', timestamp)

        old_stage = self.current_stage
        self._update_stage(current_profit_pct)
        
        old_stop_loss = self.stop_loss
        self._update_stop_loss(current_profit_pct)
        
        self.stop_loss_history.append(self.stop_loss)
        self.stage_history.append(self.current_stage)
        self.profit_history.append(current_profit_pct * 100)
        self.position_size_history.append(self.position_size)
        
        # 检查硬止损保护 (绝对不能超过的亏损)
        hard_stop_price = self.entry_price * (1 - self.hard_stop_loss_ratio)
        if price <= hard_stop_price:
            return self._exit_position(price, 'HARD_STOP_LOSS', timestamp)

        # 检查最大回撤保护
        max_profit_achieved = (self.high_water_mark - self.entry_price) / self.entry_price
        current_drawdown = (self.high_water_mark - price) / self.high_water_mark
        if max_profit_achieved > 0.05 and current_drawdown > self.max_drawdown_protection:
            return self._exit_position(price, 'MAX_DRAWDOWN_PROTECTION', timestamp)

        if price <= self.stop_loss:
            return self._exit_position(price, 'STOP_LOSS', timestamp)
        
        result = {
            'success': True, 'action': 'HOLD', 'position_type': 'LONG',
            'price': price, 'profit_pct': current_profit_pct * 100,
            'stop_loss': self.stop_loss, 'stage': self.current_stage,
            'position_size': self.position_size, 'high_water_mark': self.high_water_mark,
            'stage_changed': old_stage != self.current_stage, 'message': '继续持有'
        }
        
        if result['stage_changed']:
            self._log(f"阶段升级: {old_stage} -> {self.current_stage}, 价格={price:.6f}")
        if abs(old_stop_loss - self.stop_loss) > 1e-8:
            self._log(f"止损更新: {old_stop_loss:.6f} -> {self.stop_loss:.6f}")
            
        return result

    def _check_partial_take_profit(self, price: float, profit_pct: float):
        """检查并执行分批止盈"""
        # 按顺序检查止盈级别，每次只触发一个
        for i, (target_profit, sell_ratio) in enumerate(self.take_profit_levels):
            if not self.take_profit_triggers_hit[i] and profit_pct >= target_profit:
                # 基于原始仓位计算卖出数量，而不是当前剩余仓位
                size_to_sell = self.position_value_at_entry * sell_ratio
                # 确保不会卖出超过当前持有的仓位
                size_to_sell = min(size_to_sell, self.position_size)

                if size_to_sell > 0:  # 只有当有仓位可卖时才执行
                    realized_profit_on_this_part = size_to_sell * profit_pct
                    self.total_profit_realized += realized_profit_on_this_part
                    self.position_size -= size_to_sell
                    self.take_profit_triggers_hit[i] = True

                    log_entry = {
                        'tick': self.tick_count, 'price': price,
                        'profit_pct_at_exit': profit_pct * 100,
                        'target_profit': target_profit * 100,
                        'sell_ratio': sell_ratio, 'size_sold': size_to_sell,
                        'remaining_size': self.position_size,
                        'original_position': self.position_value_at_entry
                    }
                    self.partial_profit_log.append(log_entry)
                    self._log(f"分批止盈: 盈利{profit_pct*100:.2f}%达到{target_profit*100:.2f}%目标. "
                              f"卖出仓位 {size_to_sell:.2f} (原始仓位的{sell_ratio*100:.0f}%), 剩余 {self.position_size:.2f}", "TRADE")

                    # 每次只执行一个止盈级别，避免同时触发多个
                    break

    def _update_stage(self, profit: float):
        """更新交易阶段"""
        if self.current_stage == 0 and profit >= self.stage1_threshold:
            self.stage1_confirmation_counter += 1
            if self.stage1_confirmation_counter >= self.stage1_confirmation_ticks:
                self.current_stage = 1
        elif self.current_stage == 0 and profit < self.stage1_threshold:
            self.stage1_confirmation_counter = 0
        elif self.current_stage == 1 and profit >= self.stage2_threshold:
            self.current_stage = 2
        elif self.current_stage == 2 and profit >= self.stage3_threshold:
            self.current_stage = 3
    
    def _calculate_volatility(self) -> float:
        """计算最近价格的波动率"""
        if len(self.price_history) < self.volatility_window:
            return 0.02  # 默认2%波动率

        recent_prices = self.price_history[-self.volatility_window:]
        returns = [abs((recent_prices[i] - recent_prices[i-1]) / recent_prices[i-1])
                  for i in range(1, len(recent_prices))]
        return np.mean(returns) if returns else 0.02

    def _update_stop_loss(self, profit: float):
        """更新止损价格 (支持动态调整)"""
        volatility_adjustment = 1.0
        if self.dynamic_stop_adjustment:
            volatility = self._calculate_volatility()
            # 波动率高时放宽止损，波动率低时收紧止损
            volatility_adjustment = max(0.5, min(2.0, volatility / 0.02))

        if self.current_stage == 1:
            adjusted_ratio = self.stage1_stop_loss_ratio * volatility_adjustment
            new_stop = self.entry_price * (1 - adjusted_ratio)
            self.stop_loss = max(self.stop_loss, new_stop)
        elif self.current_stage == 2:
            new_stop = self.entry_price * (1 + self.stage2_stop_profit_ratio)
            self.stop_loss = max(self.stop_loss, new_stop)
        elif self.current_stage == 3:
            adjusted_trailing = self.trailing_ratio * volatility_adjustment
            trailing_distance = (self.high_water_mark - self.entry_price) * adjusted_trailing
            new_stop = self.high_water_mark - trailing_distance
            min_profit_stop = self.entry_price * (1 + self.min_profit_lock_ratio)
            self.stop_loss = max(self.stop_loss, new_stop, min_profit_stop)
    
    def manual_exit(self, price: float, reason: str = 'MANUAL', timestamp: Optional[datetime] = None) -> Dict:
        """手动平仓"""
        if self.position_size <= 0:
            return {'success': False, 'action': 'NO_POSITION', 'message': '当前无持仓'}
        return self._exit_position(price, reason, timestamp)
    
    def _exit_position(self, price: float, reason: str, timestamp: Optional[datetime] = None) -> Dict:
        """内部平仓处理"""
        if self.position_size <= 0 and reason != 'ALL_PARTS_TAKEN':
            return {'success': False, 'message': '无持仓'}
        
        final_profit_on_remaining = (price - self.entry_price) / self.entry_price
        final_realized_profit = self.total_profit_realized + (self.position_size * final_profit_on_remaining)
        average_profit_pct = final_realized_profit / self.position_value_at_entry * 100
        
        max_profit = (self.high_water_mark - self.entry_price) / self.entry_price
        exit_time = timestamp or datetime.now()
        holding_duration = exit_time - self.entry_time if self.entry_time else None
        
        result = {
            'success': True, 'action': 'EXIT', 'reason': reason, 'position_type': 'LONG',
            'price': price, 'entry_price': self.entry_price,
            'final_profit_pct': average_profit_pct, 
            'max_profit_pct': max_profit * 100, 'final_stage': self.current_stage,
            'holding_duration': str(holding_duration), 'exit_time': exit_time.isoformat(),
            'partial_profits_log': self.partial_profit_log,
            'message': f"平仓：原因={reason}, 最终平均收益={average_profit_pct:.2f}%" 
        }
        
        final_position_size = self.position_size
        self.position_size = 0.0 # 清仓
        
        self._log(f"平仓 (多头): 原因={reason}, 价格={price:.6f}, 剩余仓位={final_position_size:.2f}, "
                  f"最终平均收益={average_profit_pct:.2f}%, 最大浮盈={max_profit*100:.2f}%", "TRADE")
        
        return result

def translate_keys_to_chinese(data: Dict) -> Dict:
    """递归地将字典中的英文键翻译为中文。"""
    translation_map = {
        "day": "天", "reference_price": "参考价格", "k_line_high": "K线最高价", 
        "k_line_low": "K线最低价", "strategy_signal": "策略信号", "action": "动作", 
        "message": "信息", "current_strategy_status": "当前策略状态", "entry_price": "入场价格", 
        "stage": "阶段", "stop_loss": "止损价格", "realtime_profit": "实时盈利", 
        "max_profit": "最大浮盈", "current_day_high_vs_entry_ratio_growth": "当日最高价相对入场价增长率",
        "success": "成功", "reason": "原因", "position_type": "仓位类型", "price": "价格", 
        "final_profit_pct": "最终平均盈利%", "max_profit_pct": "最大浮盈%", "final_stage": "最终阶段", 
        "total_ticks": "总Tick数", "holding_duration": "持仓时长", "exit_time": "平仓时间", 
        "high_water_mark": "最高水位", "has_position": "有持仓", "current_price": "当前价格", 
        "profit_pct": "盈利%", "distance_to_stop_pct": "距止损%", "current_profit_pct": "当前盈利%", 
        "current_drawdown_pct": "当前回撤%", "price_volatility": "价格波动率", "stage_distribution": "阶段分布", 
        "error": "错误", "entry_time": "入场时间", "stock_name": "股票名称", "position_size": "当前仓位",
        "partial_profits_log": "分批止盈记录", "size_sold": "卖出份额", "remaining_size": "剩余份额"
    }
    if isinstance(data, dict):
        return {translation_map.get(k, k): translate_keys_to_chinese(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [translate_keys_to_chinese(elem) for elem in data] 
    return data

def simulate_single_trade_backtest(price_series: List[float], strategy_params: Optional[Dict] = None, verbose: bool = True) -> Dict:
    if not price_series or len(price_series) < 1: return {'final_profit_pct': 0.0, 'message': '价格序列无效'}
    if strategy_params is None: strategy_params = {}
    strategy = LiveTradingStrategy(enable_logging=False, **strategy_params) 
    entry_price = price_series[0]
    strategy.enter_position(entry_price)
    trade_outcome_info = None
    for day_idx, current_price in enumerate(price_series[1:], 1):
        if strategy.position_size <= 0: break
        if pd.isna(current_price): continue
        process_result = strategy.process_tick(current_price)
        if process_result.get('action') == 'EXIT':
            trade_outcome_info = process_result
            break
    if strategy.position_size > 0:
        trade_outcome_info = strategy.manual_exit(price_series[-1], 'END_OF_DATA')
    if trade_outcome_info is None:
        return {'final_profit_pct': 0.0, 'message': '交易未产生平仓事件', 'strategy_instance': strategy}
    return {'final_profit_pct': trade_outcome_info.get('final_profit_pct', 0.0), 'strategy_instance': strategy, 'final_result_details': trade_outcome_info}

def testBuy(buysellHistory_list, strategy_params, max_output_trades=50, detailed_output=True):
    total_net_profit, all_trade_results = 0.0, []
    print("\n=== 多组数据回测 (基于新版策略) ===")

    total_trades = len(buysellHistory_list[0])
    print(f"总交易数: {total_trades}")
    print(f"输出策略: 前20个详细 + 重要交易(盈亏>2%或每10个)")

    processed_count = 0

    for i in range(total_trades):
        trade_type = buysellHistory_list[0][i]
        
        if trade_type != 1:
            continue

        test_prices_for_this_trade = buysellHistory_list[1][i]

        if not test_prices_for_this_trade:
            continue

        processed_count += 1

        # 控制详细输出 - 只显示有盈利或亏损的重要交易
        profit = 0  # 先计算profit
        single_trade_result = simulate_single_trade_backtest(
            test_prices_for_this_trade,
            strategy_params=strategy_params,
            verbose=False
        )
        profit = single_trade_result['final_profit_pct']

        # 只显示重要交易：前20个，或者盈利>2%，或者亏损>2%
        show_details = (detailed_output and
                       (i < 20 or abs(profit) > 2.0 or i % 10 == 0))

        if show_details:
            print(f"\n--- 开始模拟第 {i+1} 组交易 ---")
        elif i == 20:
            print(f"\n... 后续只显示重要交易（大盈亏或每10个交易） ...")
        
        # profit已经在上面计算过了
        total_net_profit += profit
        all_trade_results.append(profit)

        if show_details:
            print(f"本次交易模拟结束。最终平均收益: {profit:.2f}%")

            # 显示分批止盈详情
            if single_trade_result.get('final_result_details') and single_trade_result['final_result_details'].get('partial_profits_log'):
                print("分批止盈详情:")
                total_sold = 0
                for j, log in enumerate(single_trade_result['final_result_details']['partial_profits_log'], 1):
                    original_pos = log.get('original_position', 1.0)
                    sell_pct = log['size_sold'] / original_pos * 100
                    total_sold += log['size_sold']
                    print(f"  第{j}次: 盈利{log['profit_pct_at_exit']:.2f}%时, 卖出{log['size_sold']:.2f}仓位({sell_pct:.0f}%), 剩余{log['remaining_size']:.2f}")
                print(f"  总计卖出: {total_sold:.2f}, 最终剩余: {log['remaining_size']:.2f}")

            # 显示交易结果详情
            if single_trade_result.get('final_result_details'):
                details = single_trade_result['final_result_details']
                print(f"交易详情: 入场{details.get('entry_price', 0):.4f} -> 出场{details.get('price', 0):.4f}")
                print(f"最大浮盈: {details.get('max_profit_pct', 0):.2f}%, 平仓原因: {details.get('reason', 'UNKNOWN')}")

            print(f"当前累计总净盈亏: {total_net_profit:.2f}%")
            print("-" * 80)

        # 显示进度
        if len(all_trade_results) % 50 == 0 and len(all_trade_results) > 0:
            progress = len(all_trade_results) / total_trades * 100
            print(f"\n进度: {len(all_trade_results)}/{total_trades} ({progress:.1f}%) - 当前累计: {total_net_profit:.2f}%")

    print("\n" + "="*80)
    print("=== 回测结果统计 ===")
    print("="*80)

    # 基本统计
    total_trades = len(all_trade_results)
    win_trades = sum(1 for p in all_trade_results if p > 0)
    loss_trades = sum(1 for p in all_trade_results if p < 0)
    flat_trades = total_trades - win_trades - loss_trades
    win_rate = win_trades / total_trades * 100 if total_trades > 0 else 0

    # 盈亏统计
    profits = [p for p in all_trade_results if p > 0]
    losses = [p for p in all_trade_results if p < 0]
    avg_profit = np.mean(profits) if profits else 0
    avg_loss = np.mean(losses) if losses else 0
    max_profit = max(all_trade_results) if all_trade_results else 0
    max_loss = min(all_trade_results) if all_trade_results else 0

    # 盈亏比
    profit_loss_ratio = abs(avg_profit / avg_loss) if avg_loss != 0 else float('inf')

    print(f"总交易数: {total_trades}")
    print(f"胜率: {win_rate:.2f}% ({win_trades}胜 / {loss_trades}负 / {flat_trades}平)")
    print(f"平均盈利: {avg_profit:.2f}% | 平均亏损: {avg_loss:.2f}%")
    print(f"最大盈利: {max_profit:.2f}% | 最大亏损: {max_loss:.2f}%")
    print(f"盈亏比: {profit_loss_ratio:.2f}")
    print(f"总净盈亏: {total_net_profit:.2f}%")
    print(f"平均每笔收益: {total_net_profit/total_trades:.2f}%" if total_trades > 0 else "平均每笔收益: 0.00%")
    print("="*80)

def main():
    print("\n\n" + "="*50)
    print("=== 演示: 使用新版策略进行多组数据回测 ===")

    # --- 定义新策略的参数 ---
    # 注意：这些参数是为日内交易等中短线场景设计的更符合逻辑的范例。
    # 您应该根据自己的交易品种、周期和回测结果来精细调整。
    realistic_strategy_params = {
        'stage1_threshold': 0.01,               # 盈利1%时，开始考虑进入阶段1
        'stage1_confirmation_ticks': 3,         # 需要连续3个tick价格都高于1%盈利，才确认
        'stage1_stop_loss_ratio': 0.015,        # 阶段1止损设在-1.5%
        'stage2_threshold': 0.02,               # 盈利2%进入阶段2
        'stage2_stop_profit_ratio': 0.01,       # 阶段2止损设在+1% (确保不亏)
        'stage3_threshold': 0.04,               # 盈利4%进入阶段3 (开始跟踪)
        'trailing_ratio': 0.4,                  # 跟踪止损回撤40% (例如从最高盈利8%回落到4.8%则止盈)
        'initial_stop_loss_ratio': 0.022,       # 初始止损3.5% (给予更多波动空间)
        'dynamic_stop_adjustment': True,         # 启用动态止损调整
        'volatility_window': 5,                  # 波动率计算窗口
        'max_drawdown_protection': 0.12,         # 最大回撤保护12%
        'hard_stop_loss_ratio': 0.04,            # 硬止损4% (绝对不能超过)
        # --- 分批止盈设置 (4阶段渐进式止盈策略) ---
        'take_profit_levels': [
            (0.02, 0.20),   # 盈利达到 2% 时, 卖出 20% 的仓位 (第一阶段止盈)
            (0.035, 0.25),  # 盈利达到 3.5% 时, 卖出 25% 的仓位 (第二阶段止盈)
            (0.055, 0.30),  # 盈利达到 5.5% 时, 卖出 30% 的仓位 (第三阶段止盈)
            (0.08, 1.00)    # 盈利达到 8% 时, 清仓 (卖出剩余仓位的100%)
        ]
    }
    print("\n使用的策略参数 (更符合逻辑的范例):")
    print(json.dumps(translate_keys_to_chinese(realistic_strategy_params), indent=2, ensure_ascii=False))

    # --- 为演示创建模拟数据 ---
    # 在您的环境中，请注释掉这段模拟数据，并使用您自己的真实数据加载
    print("\n*** 注意: 正在使用模拟数据进行演示。 ***")
    price_series_1 = [100, 101, 102.5, 101.5, 103.6, 105, 103, 107.1] # 触发分批止盈
    price_series_2 = [100, 101, 99, 98, 97.5] # 初始止损
    price_series_3 = [100, 102.1, 101.5, 102.2, 101.8, 102.3] # 触发阶段1确认并收紧止损
    buysellHistory_data = [
        [1, 1, 1], # 交易类型 (1=多头)
        [price_series_1, price_series_2, price_series_3], # K线最高价序列
        [[], [], []]  # K线最低价序列 (未使用)
    ]
    # --- 模拟数据结束 ---
    
    from MkKu import load_json
    buysellHistory_data = load_json('buysellHistory.json')

    # 调用 testBuy 函数来执行多组数据回测
    # 限制详细输出前50个交易，避免输出过长
    testBuy(buysellHistory_data, realistic_strategy_params, max_output_trades=50, detailed_output=True)
    
if __name__ == "__main__":
    main()
