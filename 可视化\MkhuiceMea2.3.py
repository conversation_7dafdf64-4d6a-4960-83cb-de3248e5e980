from okx_sdkV1 import get_kline_data, OKXClient

from MkKu import save_json,save_df_to_excel,get_local_kline_data,analyze_trend_changes,get_trade_decision_v2,calculate_trading_profit,plot_capital_curve_v1
from MKTrade import rolling_trade ,analyze_trading_performance,plot_trading_results

import time
import pandas as pd
import numpy as np
from typing import List, Tuple,Dict, Any
import mplfinance as mpf
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from matplotlib.font_manager import FontProperties
from typing import Dict, List, Any
import warnings
warnings.filterwarnings('ignore', category=UserWarning)
from matplotlib.font_manager import FontProperties, findfont, FontManager
import os

def setup_chinese_fonts():
    """配置中文字体"""
    plt.rcParams['font.sans-serif'] = [
        'SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 
        'WenQuanYi Micro Hei', 'sans-serif'
    ]
    plt.rcParams['axes.unicode_minus'] = False

def plot_kline_with_trade_signals(df: pd.DataFrame,
                                ohlc_cols: List[str] = ['open', 'high', 'low', 'close'],
                                time_col: str = 'open_time',
                                trade_col: str = 'tradeS',
                                state_col: str = 'emaStas',
                                title: str = 'K线图与交易信号',
                                figsize: tuple = (16, 10)):
    """
    绘制K线图并标记交易信号
    
    Args:
        df (pd.DataFrame): 包含K线和交易信号数据的DataFrame
        ohlc_cols (List[str]): OHLC四列的列名列表，默认['open', 'high', 'low', 'close']
        time_col (str): 时间列名，默认'open_time'
        trade_col (str): 交易信号列名，默认'tradeS'
        state_col (str): 状态列名，默认'emaStas'
        title (str): 图表标题
        figsize (tuple): 图表大小
    """
    
    setup_chinese_fonts()
    
    if df.empty:
        raise ValueError("DataFrame不能为空")
    
    required_cols = ohlc_cols + [time_col, trade_col]
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"缺少必需的列: {missing_cols}")
    
    df_plot = df.copy()
    
    rename_map = {
        ohlc_cols[0]: 'Open', ohlc_cols[1]: 'High', 
        ohlc_cols[2]: 'Low', ohlc_cols[3]: 'Close'
    }
    df_plot.rename(columns=rename_map, inplace=True)
    
    if time_col in df_plot.columns:
        df_plot[time_col] = pd.to_datetime(df_plot[time_col])
        df_plot.set_index(time_col, inplace=True)
    else:
        dummy_index = pd.to_datetime(pd.date_range(start='2024-01-01', periods=len(df_plot)))
        df_plot.set_index(dummy_index, inplace=True)
    
    long_signals = df_plot[df_plot[trade_col] == 1]
    short_signals = df_plot[df_plot[trade_col] == -1]
    
    addplot_list = []
    
    if not long_signals.empty:
        long_markers = pd.Series(index=df_plot.index, dtype=float)
        long_markers.loc[long_signals.index] = long_signals['Low'] * 0.998
        addplot_list.append(
            mpf.make_addplot(long_markers, type='scatter', markersize=100, 
                           marker='^', color='green', alpha=0.8)
        )
    
    if not short_signals.empty:
        short_markers = pd.Series(index=df_plot.index, dtype=float)
        short_markers.loc[short_signals.index] = short_signals['High'] * 1.002
        addplot_list.append(
            mpf.make_addplot(short_markers, type='scatter', markersize=100,
                           marker='v', color='red', alpha=0.8)
        )
    
    mc = mpf.make_marketcolors(up='g', down='r', inherit=True)
    s = mpf.make_mpf_style(marketcolors=mc, gridstyle=':')
    
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")
        
        try:
            fig, axes = mpf.plot(
                df_plot[['Open', 'High', 'Low', 'Close']], 
                type='candle', style=s, addplot=addplot_list if addplot_list else None,
                figsize=figsize, returnfig=True, show_nontrading=False
            )
        except Exception as e:
            raise RuntimeError(f"绘制K线图失败: {e}")
    
    ax = axes[0]
    ax.set_title(title, fontsize=14, pad=20, fontweight='bold')
    ax.set_ylabel('价格', fontsize=12)
    
    legend_elements = []
    if not long_signals.empty:
        legend_elements.append(plt.Line2D([0], [0], marker='^', color='w', markerfacecolor='green', markersize=10, label='做多信号 (1)', linestyle='None'))
    if not short_signals.empty:
        legend_elements.append(plt.Line2D([0], [0], marker='v', color='w', markerfacecolor='red', markersize=10, label='做空信号 (-1)', linestyle='None'))
    if legend_elements:
        ax.legend(handles=legend_elements, loc='upper left', fontsize=10)
    
    stats_text = f"交易信号统计:\n做多: {len(long_signals)}次\n做空: {len(short_signals)}次\n总计: {len(long_signals) + len(short_signals)}次"
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=9,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    try:
        mpf.show()
    except Exception as e:
        print(f"显示图表时出错: {e}")
    
    return {
        'total_signals': len(long_signals) + len(short_signals),
        'long_signals': len(long_signals),
        'short_signals': len(short_signals),
    }

def analyze_ema_state(ema_values: List[float] | Tuple[float, float, float], tolerance_percent: float = 0.005) -> str:
    if not isinstance(ema_values, (list, tuple)) or len(ema_values) != 3:
        raise ValueError("输入参数 'ema_values' 必须是一个包含3个数字的列表或元组。")
    if any(np.isnan(v) for v in ema_values):
        return "0. 数据不足"
    a, b, c = ema_values
    if b != 0 and abs(a - b) / abs(b) <= tolerance_percent: ab_state = "approx"
    elif a > b: ab_state = "greater"
    else: ab_state = "less"
    if c != 0 and abs(b - c) / abs(c) <= tolerance_percent: bc_state = "approx"
    elif b > c: bc_state = "greater"
    else: bc_state = "less"
    if ab_state == "greater":
        if bc_state == "greater": return "1. 强势多头 (A > B > C)"
        elif bc_state == "less": return "2. 下跌中反弹"
        else: return "3. 盘整后突破"
    elif ab_state == "less":
        if bc_state == "greater": return "4. 上涨中回调"
        elif bc_state == "less": return "5. 强势空头 (C > B > A)"
        else: return "6. 盘整后破位"
    else:
        if bc_state == "greater": return "7. 上涨趋势减弱"
        elif bc_state == "less": return "8. 下跌趋势减弱"
        else: return "9. 极限盘整/无趋势"

def calculate_ema(data, period):
    if not isinstance(data, pd.Series): data = pd.Series(data)
    if len(data) < period: return pd.Series(np.zeros(len(data)), index=data.index)
    return data.ewm(span=period, adjust=False).mean()

def calculate_ema_optimized(df, column, period, N=3):
    df_length = len(df)
    threshold = N * period
    if df_length < period: return pd.Series([0.0] * df_length, index=df.index)
    slice_df = df if df_length <= threshold else df.iloc[-threshold:]
    ema_result = calculate_ema(slice_df[column], period)
    if len(slice_df) < df_length:
        full_result = pd.Series([0.0] * df_length, index=df.index)
        full_result.iloc[-len(slice_df):] = ema_result.values
        return full_result
    return ema_result

def analyze_kline_performance(df: pd.DataFrame, open_direction: int) -> tuple[list[str], pd.Series]:
    if df.empty or len(df) < 2: return [open_direction, "N/A", "N/A"], pd.Series([])
    initial_price = df.iloc[0]['open']
    if initial_price == 0: return [open_direction, "N/A", "N/A"], pd.Series([])
    max_price, min_price = df['high'].max(), df['low'].min()
    if open_direction == 1:
        max_good = (max_price / initial_price) - 1
        max_bad = (min_price / initial_price) - 1
    else:
        max_good = (initial_price / min_price) - 1 if min_price != 0 else np.inf
        max_bad = (initial_price / max_price) - 1 if max_price != 0 else -np.inf
    open_safe = df['open'].replace(0, np.nan)
    pct_change = ((df['close'] - df['open']) / open_safe).apply(lambda x: f"{x:.3%}" if pd.notna(x) else "NaN%")
    return [open_direction, f"{max_good:.3%}", f"{max_bad:.3%}"], pct_change

def calculate_trading_signals(slice_df, price_columns, price_columns_reversed, periods, tolerance_percent, buy_nowSigle, sell_nowSigle):
    LivemaS_buy = [calculate_ema_optimized(slice_df, col, p, N=3).iloc[-1] for col, p in zip(price_columns, periods)]
    state_for_buy = analyze_ema_state(LivemaS_buy, tolerance_percent)
    trade_signal_buy, reason_buy = get_trade_decision_v2(buy_nowSigle, int(state_for_buy[0]), 'flat', 'both')
    LivemaS_sell = [calculate_ema_optimized(slice_df, col, p, N=3).iloc[-1] for col, p in zip(price_columns_reversed, periods)]
    state_for_sell = analyze_ema_state(LivemaS_sell, tolerance_percent)
    trade_signal_sell, reason_sell = get_trade_decision_v2(sell_nowSigle, int(state_for_sell[0]), 'flat', 'both')
    return trade_signal_buy, reason_buy, state_for_buy, LivemaS_buy, trade_signal_sell, reason_sell, state_for_sell, LivemaS_sell, int(state_for_buy[0]), int(state_for_sell[0])

def process_trade_signal(trade_signal_buy, trade_signal_sell, reason_buy, reason_sell, slice_df, any_df, start_idx, count):
    current_trade_signal, current_trade_price, current_ema_state, good_bad = 0, 0, 0, 0
    if trade_signal_buy == 'BUY':
        current_trade_signal = 1
        current_trade_price = slice_df.iloc[-1]['close']
        good_bad, _ = analyze_kline_performance(any_df, 1)
        print(f"  [信号] 第{start_idx + count}根 K线: 发现 BUY 信号. 原因: {reason_buy}. 价格: {current_trade_price:.5f}")
    elif trade_signal_sell == 'SHORT':
        current_trade_signal = -1
        current_trade_price = slice_df.iloc[-1]['close']
        good_bad, _ = analyze_kline_performance(any_df, -1)
        print(f"  [信号] 第{start_idx + count}根 K线: 发现 SHORT 信号. 原因: {reason_sell}. 价格: {current_trade_price:.5f}")
    return current_trade_signal, current_trade_price, current_ema_state, good_bad

def update_result_lists(result_lists, data_tuple):
    for lst, item in zip(result_lists, data_tuple):
        lst.append(item)

def plot_independent_performance(long_stats: dict, short_stats: dict):
    """
    独立分析并绘制多头、空头及合并策略的业绩曲线。
    """
    setup_chinese_fonts()
    plt.style.use('seaborn-v0_8-darkgrid')
    fig, ax = plt.subplots(figsize=(14, 8))

    report_lines = ["="*50, "独立策略业绩分析报告", "="*50]
    
    long_df = pd.DataFrame(long_stats)
    if not long_df.empty:
        long_df['cum_pnl'] = long_df['change_pct'].cumsum()
        ax.plot(long_df.index, long_df['cum_pnl'], marker='o', linestyle='-', label=f"多头策略收益 ({len(long_df)}笔)", color='crimson', markersize=4)
        win_rate_long = (long_df['change_pct'] > 0).mean() * 100
        report_lines.append(f"\n--- 多头策略 ---\n总交易次数: {len(long_df)}\n胜率: {win_rate_long:.2f}%\n累计收益: {long_df['cum_pnl'].iloc[-1]:.2f}%")
    else:
        report_lines.append("\n--- 多头策略 ---\n无交易记录")

    short_df = pd.DataFrame(short_stats)
    if not short_df.empty:
        short_df['cum_pnl'] = short_df['change_pct'].cumsum()
        ax.plot(short_df.index, short_df['cum_pnl'], marker='x', linestyle='--', label=f"空头策略收益 ({len(short_df)}笔)", color='forestgreen', markersize=5)
        win_rate_short = (short_df['change_pct'] > 0).mean() * 100
        report_lines.append(f"\n--- 空头策略 ---\n总交易次数: {len(short_df)}\n胜率: {win_rate_short:.2f}%\n累计收益: {short_df['cum_pnl'].iloc[-1]:.2f}%")
    else:
        report_lines.append("\n--- 空头策略 ---\n无交易记录")

    if not long_df.empty or not short_df.empty:
        combined_df = pd.concat([long_df, short_df]).sort_values(by='close_time').reset_index(drop=True)
        combined_df['cum_pnl'] = combined_df['change_pct'].cumsum()
        ax.plot(combined_df.index, combined_df['cum_pnl'], marker='.', linestyle='-', label=f"合并策略总收益 ({len(combined_df)}笔)", color='royalblue', linewidth=2.5)
        win_rate_combined = (combined_df['change_pct'] > 0).mean() * 100
        report_lines.append(f"\n--- 合并策略 ---\n总交易次数: {len(combined_df)}\n胜率: {win_rate_combined:.2f}%\n累计收益: {combined_df['cum_pnl'].iloc[-1]:.2f}%")
    else:
        report_lines.append("\n--- 合并策略 ---\n无交易记录")
    
    report_lines.append("="*50)
    print("\n".join(report_lines))

    ax.set_title('多空独立策略业绩对比', fontsize=18, fontweight='bold')
    ax.set_xlabel('交易次数', fontsize=12)
    ax.set_ylabel('累计收益 (%)', fontsize=12)
    ax.legend(fontsize=11, frameon=True, shadow=True)
    ax.axhline(0, color='black', linestyle='--', linewidth=1, alpha=0.5)
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
     
    Uname='doge'
    Ktime='3m'
    strTime='2025-4-1'
    endTime='2025-6-10'
    
    try:
        df = get_local_kline_data(Uname, Ktime, strTime, endTime)
    except Exception as e:
        print(f"从本地加载数据失败: {e}")
        df = pd.DataFrame() 
    
    if df.empty:
        print("未能加载K线数据，程序终止。")
    else:
        print(f"分析数据量: {len(df)} 条K线")
        print(f"数据时间范围: {df['open_time'].iloc[0]} 到 {df['open_time'].iloc[-1]}")
    
        # --- 策略开关 ---
        # 可选: 'both', 'long_only', 'short_only'
        trade_direction = 'both'
        print(f"\n当前运行模式: {trade_direction}\n")

        count=100
        max_start_index = len(df) - count
        buy_lastSigle, buy_nowSigle, sell_lastSigle, sell_nowSigle = 0, 0, 0, 0
        
        # --- 独立状态管理 ---
        long_position_active = False
        short_position_active = False
        active_long_order = None
        active_short_order = None
        
        periods=[8, 16, 32]
        tolerance_percent=0.0015
        price_columns=['low', 'close', 'high']
        price_columns_reversed = price_columns[::-1]
        goodBads=24
        
        # 结果记录列表
        tradeS, tradePrice, emaStas, any_good_bad = [], [], [], []
        emaStasBuy, emaStasSell = [], []
        emaTrades=[[],[]]

        # 独立业绩统计
        long_trade_stats = {'direction': [], 'open_price': [], 'close_price': [], 'change_pct': [], 'close_time': []}
        short_trade_stats = {'direction': [], 'open_price': [], 'close_price': [], 'change_pct': [], 'close_time': []}
        
        # 外部模块和实例
        from Mkjson import *
        from Mk_OKX_zyzs import *
        from Mk_OKX_zyzsBuyPro1 import LiveTradingStrategy
        from Mk_OKX_zyzsSellPro import RealTimeTradingStrategy
        all_orders = load_orders()
        strategyBuy = LiveTradingStrategy() 
        strategySell = RealTimeTradingStrategy(show_details=True)

        # --- 主循环开始 ---
        for start_idx in range(max_start_index + 1):
            end_idx = start_idx + count
            slice_df = df.iloc[start_idx:end_idx]
            any_df = df.iloc[end_idx:end_idx+goodBads] if end_idx + goodBads <= len(df) else df.iloc[end_idx:]
            if any_df.empty: continue

            kHigh, Klow, kClose, KtimeSliceDf = slice_df.iloc[-1][['high', 'low', 'close', 'open_time']]
            
            # 1. 计算信号
            (trade_signal_buy, reason_buy, state_for_buy, LivemaS_buy,
             trade_signal_sell, reason_sell, state_for_sell, LivemaS_sell,
             buy_nowSigle, sell_nowSigle) = calculate_trading_signals(
                slice_df, price_columns, price_columns_reversed, periods, tolerance_percent,
                buy_nowSigle, sell_nowSigle
            )
            
            # 2. 记录信号信息
            current_trade_signal, current_trade_price, _, good_bad = process_trade_signal(
                trade_signal_buy, trade_signal_sell, reason_buy, reason_sell,
                slice_df, any_df, start_idx, count
            )
            
            # --- 多头策略处理 ---
            if trade_direction in ['long_only', 'both']:
                if not long_position_active:
                    if trade_signal_buy == 'BUY':
                        long_position_active = True
                        entry_price = kClose * 0.9995
                        print(f"✅ [执行开多] 价格: {entry_price:.5f}, 时间: {KtimeSliceDf}")
                        active_long_order = open_new_order(
                            orders=all_orders, timestamp_str=KtimeSliceDf, open_price=entry_price, direction=1,
                            position_quantity=100, take_profit_pct=0.05, stop_loss_pct=0.01
                        )
                        strategyBuy.enter_position(entry_price)
                        save_orders(all_orders)
                else:
                    realtime_output = strategyBuy.process_tick(kHigh, Klow)
                    if realtime_output['策略信号']['动作'] == 'EXIT':
                        long_position_active = False
                        close_price = kClose
                        open_p = active_long_order['开仓价格']
                        change_pct = ((close_price - open_p) / open_p) * 100
                        print(f"❌ [执行平多] 原因: {realtime_output['策略信号']['信息']}. 价格: {close_price:.5f}, 盈亏: {change_pct:.2f}%")
                        update_order_close(orders=all_orders, order_id=active_long_order["order_id"], close_price=close_price, timestamp_str=KtimeSliceDf)
                        long_trade_stats['direction'].append('buy')
                        long_trade_stats['open_price'].append(open_p)
                        long_trade_stats['close_price'].append(close_price)
                        long_trade_stats['change_pct'].append(change_pct)
                        long_trade_stats['close_time'].append(KtimeSliceDf)
                        active_long_order = None

            # --- 空头策略处理 ---
            if trade_direction in ['short_only', 'both']:
                if not short_position_active:
                    if trade_signal_sell == 'SHORT':
                        short_position_active = True
                        entry_price = kClose * 1.0005
                        print(f"✅ [执行开空] 价格: {entry_price:.5f}, 时间: {KtimeSliceDf}")
                        active_short_order = open_new_order(
                            orders=all_orders, timestamp_str=KtimeSliceDf, open_price=entry_price, direction=-1,
                            position_quantity=100, take_profit_pct=0.05, stop_loss_pct=0.01
                        )
                        strategySell.enter_position(entry_price)
                        save_orders(all_orders)
                else:
                    action_result = strategySell.process_price(Klow, jixian_price=kHigh)
                    if action_result == 'STOP_LOSS':
                        short_position_active = False
                        close_price = kClose
                        open_p = active_short_order['开仓价格']
                        change_pct = ((open_p - close_price) / open_p) * 100
                        sell_output = strategySell.get_current_status(price=Klow, k_line_high=kHigh, k_line_low=Klow)
                        print(f"❌ [执行平空] 原因: {sell_output['策略信号']['信息']}. 价格: {close_price:.5f}, 盈亏: {change_pct:.2f}%")
                        update_order_close(orders=all_orders, order_id=active_short_order["order_id"], close_price=close_price, timestamp_str=KtimeSliceDf)
                        short_trade_stats['direction'].append('sell')
                        short_trade_stats['open_price'].append(open_p)
                        short_trade_stats['close_price'].append(close_price)
                        short_trade_stats['change_pct'].append(change_pct)
                        short_trade_stats['close_time'].append(KtimeSliceDf)
                        active_short_order = None

            # 更新用于分析的原始信号列表
            update_result_lists(
                [emaStasBuy, emaStasSell, emaTrades[0], emaTrades[1], tradeS, tradePrice, emaStas, any_good_bad],
                (f'{LivemaS_buy[0]:.5f}-{LivemaS_buy[1]:.5f}-{LivemaS_buy[2]:.5f}', f'{LivemaS_sell[0]:.5f}-{LivemaS_sell[1]:.5f}-{LivemaS_sell[2]:.5f}',
                 state_for_buy, state_for_sell, current_trade_signal, current_trade_price, state_for_buy if current_trade_signal==1 else state_for_sell, good_bad)
            )
        # --- 主循环结束 ---
        
        # --- 独立业绩分析与绘图 ---
        plot_independent_performance(long_trade_stats, short_trade_stats)
     
        # 填充数据以便在原始DataFrame中查看
        pad_length = len(df) - len(tradeS)
        def pad_list(lst, count, pad_len):
            return [0] * (count - 1) + lst + [0] * pad_len
        
        df['tradeS'] = pad_list(tradeS, count, pad_length)[:len(df)]
        df['tradePrice'] = pad_list(tradePrice, count, pad_length)[:len(df)]
        df["any_good_bad"] = pad_list(any_good_bad, count, pad_length)[:len(df)]
        df['emaBuy'] = pad_list(emaStasBuy, count, pad_length)[:len(df)]
        df['emaSell'] = pad_list(emaStasSell, count, pad_length)[:len(df)]
        df['state_for_buy'] = pad_list(emaTrades[0], count, pad_length)[:len(df)]
        df['state_for_sell'] = pad_list(emaTrades[1], count, pad_length)[:len(df)]
        
        save_df_to_excel(df, 'independent_strategy_result_final')
        
        # 绘制原始信号K线图
        plot_kline_with_trade_signals(df, title='原始交易信号图 (非实际成交)')
