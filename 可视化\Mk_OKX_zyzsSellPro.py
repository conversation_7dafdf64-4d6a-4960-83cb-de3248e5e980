import numpy as np
import json 

# --- 猴子补丁开始 ---
if not hasattr(np, 'NaN'):
    np.NaN = np.nan
# --- 猴子补丁结束 ---

class RealTimeTradingStrategy:
    """
    实盘交易用的动态止损策略
    可以逐个处理价格，维持交易状态
    """
    
    def __init__(self, show_details=True):
        """
        初始化策略。
        现在不包含入场价格，所有与交易状态相关的初始化都将放到 enter_position 方法中。
        
        参数:
        show_details: 是否显示详细信息
        """
        self.show_details = show_details
        
        # === 策略参数 (针对空头优化) ===
        self.initial_stop_loss = 0.04      # 初始止损+2% (抗住反弹)
        self.stage1_threshold = 0.008      # 0.8%盈利进入阶段1
        self.stage2_threshold = 0.018      # 1.8%盈利进入阶段2
        self.stage3_threshold = 0.025      # 2.5%盈利进入阶段3
        self.stage1_stop_loss = 0.012      # 阶段1止损+1.2%
        self.stage2_stop_profit = 0.005    # 阶段2止损设在-0.5%(盈利区)
        self.trailing_ratio = 0.35         # 跟踪止损35%回撤
        
        # === 交易状态初始化 ===
        # 这些变量将在 enter_position 方法中设置
        self.entry_price = np.NaN
        self.position = False  # 是否持有仓位，默认无仓位
        self.stage = 0         # 当前阶段
        self.stop_loss = np.NaN
        self.lowest_price = np.NaN # 对于空头，这是最高盈利点（价格最低）
        self.highest_price = np.NaN # 对于空头，这是最高亏损点（价格最高），用于回撤计算的最高点
        self.day_count = 0     # 天数计数
        self.max_loss = 0      # 历史最大亏损（相对入场价的价格上涨百分比）
        self.last_processed_price = np.NaN # 最后一次处理的价格，用于实时盈利计算
        self.max_profit_achieved_value = 0.0 # 记录历史达到的最高盈利百分比（基于entry_price和lowest_price）

        # 新增用于存储最近一次策略信号和消息的成员变量
        self.latest_action = "NO_POSITION"
        self.latest_message = "策略初始化，等待入场"
        # 新增用于存储每次交易最终盈利的变量，用于 get_current_status 获取平仓后数据
        self.final_trade_profit = 0.0 


        if self.show_details:
            print("=== 空头动态止损策略初始化完成 ===")
            print("请调用 .enter_position(price) 方法进行入场。")
            print("-" * 50)

    def enter_position(self, entry_price):
        """
        进入空头仓位，设置入场价格并初始化所有交易状态。
        
        参数:
        entry_price: 入场价格
        
        返回:
        dict: 包含入场信息的字典 (注意：此方法的返回字典格式可根据实际需要调整，但通常不包含K线信息)
        """
        if self.position:
            print("警告: 策略已持有仓位，请先平仓再尝试入场。")
            # 更新最新信号
            self.latest_action = 'ALREADY_IN_POSITION'
            self.latest_message = '策略已持有仓位，无法再次入场'
            return {
                'action': self.latest_action,
                'message': self.latest_message,
                'entry_price': self.entry_price
            }

        self.entry_price = entry_price
        self.position = True
        self.stage = 0
        self.stop_loss = entry_price * (1 + self.initial_stop_loss)  # 空头止损在上方
        self.lowest_price = entry_price  # 记录最低价(空头的高水位，即最高浮盈时的价格)
        self.highest_price = entry_price # 初始最高价也设为入场价
        self.day_count = 0     # 重新计数天数
        self.max_loss = 0      # 重新初始化最大亏损
        self.last_processed_price = entry_price # 记录入场价格作为第一个处理价格
        self.max_profit_achieved_value = 0.0 # 重新初始化最高盈利值
        self.final_trade_profit = 0.0 # 重置最终交易盈利

        # 更新最新信号
        self.latest_action = 'ENTER_POSITION'
        self.latest_message = f'成功入场，入场价格: {self.entry_price:.5f}'

        if self.show_details:
            print("=== 空头动态止损策略入场 ===")
            print(f"开仓价格: {self.entry_price:.5f}")
            print(f"初始止损: {self.stop_loss:.5f} (+{self.initial_stop_loss*100:.1f}%)")
            print("-" * 50)
            
        return {
            'action': self.latest_action,
            'message': self.latest_message,
            'entry_price': self.entry_price,
            'initial_stop_loss': self.stop_loss
        }
    
    def process_price(self, current_price, jixian_price=None):
        """
        处理单个价格点，并更新策略内部状态。
        此方法不再返回复杂的字典，只返回一个指示操作的简单信号。
        
        参数:
        current_price: 当前价格 (K线最低价)
        jixian_price: 极限价格（通常是K线最高价，用于计算最大理论亏损和K线高价）
        
        返回:
        str: 表示执行动作的字符串 ('HOLD', 'STOP_LOSS', 'NO_POSITION_YET')
        """
        # 如果没有仓位，仅更新最新信号并返回
        if not self.position:
            self.latest_action = 'NO_POSITION'
            self.latest_message = '已无仓位'
            self.final_trade_profit = 0.0 # 如果无仓位，最终盈利设为0
            return 'NO_POSITION'
        
        self.day_count += 1
        self.last_processed_price = current_price # 更新最后处理的价格

        # 更新最低价(空头最有利位置，即价格越低，浮盈越高)
        self.lowest_price = min(self.lowest_price, current_price)
        # 更新最高价 (空头最不利位置，即价格越高，浮亏越大)
        self.highest_price = max(self.highest_price, current_price)
        
        # 计算当前盈利(空头: (开仓价 - 当前价) / 开仓价)
        current_profit = (self.entry_price - current_price) / self.entry_price
        
        # 更新历史最高盈利百分比
        max_profit_current_calc = (self.entry_price - self.lowest_price) / self.entry_price
        self.max_profit_achieved_value = max(self.max_profit_achieved_value, max_profit_current_calc)

        # === 阶段升级逻辑 ===
        old_stage = self.stage
        
        # 阶段0 -> 阶段1: 达到阶段1盈利阈值
        if self.stage == 0 and current_profit >= self.stage1_threshold:
            self.stage = 1
            new_stop = self.entry_price * (1 + self.stage1_stop_loss)
            self.stop_loss = min(self.stop_loss, new_stop) 
            
        # 阶段1 -> 阶段2: 达到阶段2盈利阈值
        elif self.stage == 1 and current_profit >= self.stage2_threshold:
            self.stage = 2
            new_stop = self.entry_price * (1 - self.stage2_stop_profit)
            self.stop_loss = min(self.stop_loss, new_stop) 
            
        # 阶段2 -> 阶段3: 达到阶段3盈利阈值
        elif self.stage == 2 and current_profit >= self.stage3_threshold:
            self.stage = 3
        
        # === 阶段3跟踪止损 ===
        if self.stage == 3:
            profit_range = self.entry_price - self.lowest_price
            trailing_distance = profit_range * self.trailing_ratio
            new_stop = self.lowest_price + trailing_distance
            self.stop_loss = min(self.stop_loss, new_stop) 
        
        # 显示阶段升级信息
        if self.show_details and old_stage != self.stage:
            print(f"第{self.day_count:2d}天: 阶段{old_stage}→{self.stage} | 价格:{current_price:.5f} | 盈利:{current_profit*100:.2f}% | 止损:{self.stop_loss:.5f}")
        
        # 更新历史最大亏损 (如果提供了极限价格，通常是当日最高价)
        if jixian_price is not None:
            current_loss_from_entry_original_calc = (jixian_price - self.entry_price) / self.entry_price
            if current_loss_from_entry_original_calc > self.max_loss:
                self.max_loss = current_loss_from_entry_original_calc
            
        # === 检查止损 (空头: 价格上涨触发止损) ===
        if current_price >= self.stop_loss:
            self.position = False 
            final_profit = (self.entry_price - current_price) / self.entry_price * 100
            
            # 更新最新信号和最终盈利
            self.latest_action = 'STOP_LOSS'
            self.latest_message = f'止损出场，收益: {final_profit:.2f}%'
            self.final_trade_profit = final_profit # 存储本次交易的最终盈利

            if self.show_details:
                max_profit_for_display = (self.entry_price - self.lowest_price) / self.entry_price * 100
                print(f"第{self.day_count:2d}天: 🛑 触发止损!")
                print(f"当前价格: {current_price:.5f} (相对于开仓价上涨: {((current_price-self.entry_price)/self.entry_price*100):.2f}%)")
                print(f"止损价格: {self.stop_loss:.5f}")
                print(f"最终收益: {final_profit:.2f}%")
                print(f"最大浮盈: {max_profit_for_display:.2f}%")
                print(f"最终阶段: {self.stage}")
            
            return 'STOP_LOSS'
        
        # === 继续持有 ===
        current_profit_for_message = (self.entry_price - current_price) / self.entry_price * 100
        self.latest_action = 'HOLD'
        self.latest_message = f'持有中，当前盈利: {current_profit_for_message:.2f}%，阶段: {self.stage}'
        self.final_trade_profit = 0.0 # 持有中，重置最终盈利为0，因为它不是最终平仓
        return 'HOLD'
    
    def manual_exit(self, exit_price):
        """
        手动平仓
        
        参数:
        exit_price: 平仓价格
        
        返回:
        str: 表示执行动作的字符串 ('MANUAL_EXIT', 'NO_POSITION_TO_EXIT')
        """
        if not self.position:
            self.latest_action = 'NO_POSITION_TO_EXIT'
            self.latest_message = '当前无仓位可平仓'
            self.final_trade_profit = 0.0 # 如果无仓位，最终盈利设为0
            return 'NO_POSITION_TO_EXIT'
        
        self.position = False 
        final_profit = (self.entry_price - exit_price) / self.entry_price * 100
        max_profit = (self.entry_price - self.lowest_price) / self.entry_price * 100
        
        self.latest_action = 'MANUAL_EXIT'
        self.latest_message = f'手动平仓，收益: {final_profit:.2f}%'
        self.final_trade_profit = final_profit # 存储本次交易的最终盈利

        if self.show_details:
            print(f"🎯 手动平仓!")
            print(f"平仓价格: {exit_price:.5f}")
            print(f"最终收益: {final_profit:.2f}%")
            print(f"最大浮盈: {max_profit:.2f}%")
            print(f"最终阶段: {self.stage}")
            print(f"持有天数: {self.day_count}")
        
        return 'MANUAL_EXIT'
    
    def get_current_status(self, price=np.NaN, k_line_high=np.NaN, k_line_low=np.NaN):
        """
        获取当前策略状态，并按照指定格式返回中文键的字典。
        包含最新的策略信号（动作和信息）。
        
        参数:
        price: 当前参考价格，用于计算实时盈利和回撤。
        k_line_high: 当前K线最高价。
        k_line_low: 当前K线最低价。
        """
        # 如果没有仓位 (已经平仓)，则返回平仓后的最终状态
        if not self.position:
            # 只有当动作是 STOP_LOSS 或 MANUAL_EXIT 时，才使用 self.final_trade_profit
            # 否则（例如 NO_POSITION），实时盈利为 0
            profit_to_display = self.final_trade_profit if self.latest_action in ['STOP_LOSS', 'MANUAL_EXIT'] else 0.0
            
            return {
                "参考价格": price, 
                "K线最高价": k_line_high if k_line_high is not None else np.NaN, 
                "K线最低价": k_line_low if k_line_low is not None else np.NaN,  
                "策略信号": {
                    "动作": self.latest_action, # 直接使用最新的动作
                    "信息": self.latest_message # 直接使用最新的信息
                },
                "当前策略状态": {
                    "开仓价格": self.entry_price if self.entry_price is not np.NaN else np.NaN,
                    "阶段": self.stage,
                    "止损位": self.stop_loss if self.stop_loss is not np.NaN else np.NaN,
                    "实时盈利": profit_to_display, # 使用存储的最终盈利
                    "最高盈利": (self.entry_price - self.lowest_price) / self.entry_price * 100 if self.entry_price is not np.NaN and self.entry_price != 0 else 0.0, # 依然显示本次交易最高盈利
                    "当前回撤": 0.0 # 平仓后回撤归零
                }
            }
        
        # 如果仍有仓位 (持有中)，则计算实时盈利和回撤
        realtime_profit_val = (self.entry_price - price) / self.entry_price * 100 if self.entry_price is not np.NaN and self.entry_price != 0 else 0.0

        max_profit_val = (self.entry_price - self.lowest_price) / self.entry_price * 100 if self.entry_price is not np.NaN and self.entry_price != 0 else 0.0
        
        current_drawdown_value = 0.0
        if self.lowest_price is not np.NaN and self.lowest_price != 0 and price > self.lowest_price:
            current_drawdown_value = (price - self.lowest_price) / self.lowest_price 
        
        return {
            "参考价格": price, 
            "K线最高价": k_line_high if k_line_high is not None else np.NaN, 
            "K线最低价": k_line_low if k_line_low is not None else np.NaN,  
            "策略信号": {
                "动作": self.latest_action, # 使用最新的动作
                "信息": self.latest_message # 使用最新的信息
            },
            "当前策略状态": {
                "开仓价格": self.entry_price,
                "阶段": self.stage,
                "止损位": self.stop_loss,
                "实时盈利": realtime_profit_val,
                "最高盈利": max_profit_val, 
                "当前回撤": current_drawdown_value * 100 
            }
        }


# backtest_with_realtime_strategy 函数保持不变，因为其逻辑没有直接的bug，
# 但它在实际使用时需要确保传入的jixianprice参数是正确的。
# 注意：这个函数已经过时，因为 RealTimeTradingStrategy 的使用方式改变了，
# 但是为了保持与用户原始代码的兼容性，我暂时保留其声明。
# 实际使用中，推荐直接在 __main__ 中按新的方式实例化和调用。
def backtest_with_realtime_strategy(prices, jixianprice=None, entry_price=None, show_details=True):
    """
    使用实时策略类进行回测 (此函数与新的策略实例化方式不完全兼容，请参考 __main__ 中的示例)
    """
    if entry_price is None:
        entry_price = prices[0]
    
    strategy = RealTimeTradingStrategy(show_details) 
    strategy.enter_position(entry_price) 

    for i, price in enumerate(prices[1:], 1): 
        jixian = jixianprice[i] if jixianprice and len(jixianprice) > i else None
        
        action = strategy.process_price(price, jixian) # process_price 现在只返回动作字符串
        
        if action == 'STOP_LOSS':
            # 如果是止损，直接获取最终状态并返回
            return strategy.get_current_status(price=price, k_line_high=jixian, k_line_low=price)
        
        if show_details and i % 15 == 0:
            # 这里的打印现在会依赖 strategy.latest_message
            print(f"第{i:2d}天: {strategy.latest_message}")
    
    if strategy.position:
        strategy.manual_exit(prices[-1]) # manual_exit 也只返回动作字符串
        return strategy.get_current_status(price=prices[-1], k_line_high=np.NaN, k_line_low=prices[-1])


# translate_keys_to_chinese 函数不再需要，可以删除。
# 为避免对外部依赖造成问题，暂时保留，但已不再在 main 中调用。
def translate_keys_to_chinese(data):
    # 此函数已不再用于 RealTimeTradingStrategy 的结果，因为其已直接返回中文键
    # 保留此函数以兼容可能存在的其他用途
    output_key_map = {
        "reference_price": "参考价格",
        "k_line_high": "K线最高价",
        "k_line_low": "K线最低价",
        "strategy_signal": "策略信号",
        "current_strategy_status": "当前策略状态",
        "max_historical_theoretical_loss": "历史最大理论亏损",
        "current_day_high_vs_entry_ratio_growth": "当前相对于开仓价上涨百分比" 
    }
    signal_key_map = {
        "action": "动作",
        "message": "信息"
    }
    status_key_map = {
        "entry_price": "开仓价格",
        "stage": "阶段",
        "stop_loss": "止损位",
        "realtime_profit": "实时盈利", 
        "max_profit": "最高盈利", 
        "current_drawdown": "当前回撤", 
    }

    if isinstance(data, dict):
        new_dict = {}
        for k, v in data.items():
            if k in status_key_map:
                new_key = status_key_map[k]
            elif k in signal_key_map:
                new_key = signal_key_map[k]
            elif k in output_key_map:
                new_key = output_key_map[k]
            else:
                new_key = k 
            new_dict[new_key] = translate_keys_to_chinese(v) 
        return new_dict
    elif isinstance(data, list):
        return [translate_keys_to_chinese(elem) for elem in data] 
    else:
        return data 


# 使用示例
if __name__ == "__main__":
    from MkKu import load_json # 假设 MkKu 模块和 load_json 函数可用

    buysellHistory = load_json('buysellHistory.json')
    
    print("=== 实盘模拟模式启动 ===")
    resultBUyS = 0 
    print(buysellHistory[0])
   
    # 外层循环遍历每个交易记录
    for i in range(len(buysellHistory[0])):
        # 只处理空头交易（-1）
        if buysellHistory[0][i] == -1:
            test_prices = buysellHistory[2][i]  # K线最低价序列
            jixian_price_series = buysellHistory[1][i] # K线最高价序列

            # 数据有效性检查
            if not test_prices:
                print(f"警告: buysellHistory[2][{i}] 为空，跳过此回测。")
                continue
            if not jixian_price_series:
                print(f"警告: buysellHistory[1][{i}] 为空，最大亏损计算可能不准确。")
            
            entry_price = test_prices[0] # 入场价格使用当前价格序列的第一个价格
            
            # 初始化策略实例并入场
            strategy = RealTimeTradingStrategy(show_details=True) 
            entry_result = strategy.enter_position(entry_price) 

            print(f"当前价格序列长度 (lenPrice): {len(test_prices)}")
            
            trade_outcome = None # 用于存储每次交易的最终结果字典（完整输出格式）

            # 内层循环逐天（或逐个价格点）回测
            for day, price in enumerate(test_prices, 1):
                current_jixian_price = None
                if jixian_price_series and (day - 1) < len(jixian_price_series):
                    current_jixian_price = jixian_price_series[day - 1]
                
                # 调用策略处理当前价格，它会更新策略内部的latest_action和latest_message，
                # 并在平仓时更新 final_trade_profit
                action_result = strategy.process_price(price, jixian_price=current_jixian_price)
                
                # 直接通过 get_current_status 获取完整且已包含最新信号的中文状态字典
                daily_output = strategy.get_current_status(price=price, 
                                                           k_line_high=current_jixian_price, 
                                                           k_line_low=price)
                
                # 打印格式化后的每日输出字典
                print(json.dumps(daily_output, indent=4, ensure_ascii=False))

                # 判断是否触发止损
                if action_result == 'STOP_LOSS': 
                    print("止损触发，交易结束!")
                    # 当止损发生时，daily_output 已经包含了最终的止损信息和盈利
                    trade_outcome = daily_output 
                    # 打印最终结果的信息
                    print(f"最终结果: {trade_outcome['策略信号']['信息']}") 
                    break # 跳出内层循环，结束当前交易模拟
                
                # 如果循环到达数据终点且策略仍有仓位，则进行手动平仓
                # 注意：这个条件只有在遍历完所有价格后才可能触发
                if strategy.position and (day == len(test_prices)):
                    strategy.manual_exit(price) # 调用手动平仓，它会更新 latest_action/message 和 final_trade_profit
                    # 获取手动平仓后的最终状态，这个状态包含了最终的盈利信息
                    trade_outcome = strategy.get_current_status(price=price, 
                                                               k_line_high=current_jixian_price, 
                                                               k_line_low=price)
                    print(f"模拟结束，手动平仓结果: {trade_outcome['策略信号']['信息']}")
            
            # --- 每次交易模拟结束后的盈亏累加部分 ---
            # 确保 trade_outcome 存在，并且其中包含我们期望的盈利路径
            # 只有当动作是 'STOP_LOSS' 或 'MANUAL_EXIT' 时才累加最终盈利
            if trade_outcome and \
               trade_outcome["策略信号"]["动作"] in ['STOP_LOSS', 'MANUAL_EXIT'] and \
               "当前策略状态" in trade_outcome and \
               "实时盈利" in trade_outcome["当前策略状态"]:
                
                resultBUyS += trade_outcome["当前策略状态"]["实时盈利"]
            # 对于其他非交易性结束或无仓位的情况，不累加任何盈利
            elif trade_outcome and "策略信号" in trade_outcome and \
                 (trade_outcome["策略信号"]["动作"] == 'NO_POSITION' or \
                  trade_outcome["策略信号"]["动作"] == 'NO_POSITION_TO_EXIT' or \
                  trade_outcome["策略信号"]["动作"] == 'ALREADY_IN_POSITION'):
                pass # 不进行累加
            
            # 打印本次交易模拟结束后的总历史盈亏
            print(f"本次交易模拟结束。当前总历史盈亏 (resultBUyS): {resultBUyS:.2f}%")
            print("-" * 60) # 分隔线，方便查看每次交易的结果